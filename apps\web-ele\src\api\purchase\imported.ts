import { requestClient } from '#/api/request';

/**
 * 查询国外进口商品数据列表
 * 用于获取所有国外进口商品的数据，支持分页和筛选
 * @param data 查询参数，可包含分页信息、筛选条件等
 * @returns 返回国外进口商品列表数据
 */
export async function getImportedList(data: any) {
  return requestClient.post<any>('/purchase-order/getPurchaseOrderAll', data);
}

/**
 * 新增或者更新国外进口商品数据
 * 当data中包含id时为更新操作，否则为新增操作
 * @param data 国外进口商品数据，包含商品编号、名称、原产国等信息
 * @returns 返回操作结果
 */
export async function addOrEditImported(data: any) {
  return requestClient.post<any>('/purchase-order/addOrEditPurchaseOrder', data);
}

/**
 * 查询国外进口商品明细数据
 * 根据ID获取单条国外进口商品的详细信息
 * @param data 包含id的查询参数
 * @returns 返回单条国外进口商品的详细信息
 */
export async function getImportedInfo(data: any) {
  return requestClient.post<any>('/purchase-order/getPurchaseOrder', data);
}

/**
 * 删除国外进口商品数据
 * 根据ID删除指定的国外进口商品记录
 * @param data 包含id的参数对象
 * @returns 返回删除操作的结果
 */
export async function deletePurchase(data: any) {
  return requestClient.post<any>('/purchase-order/deletePurchase', data);
}


/**
 * 查询国外进口商品明细列表
 * 根据主表ID获取对应的明细数据列表
 * @param data 包含主表ID的查询参数
 * @returns 返回明细数据列表
 */
export async function getPurchaseOrderDetailAll(data: any) {
  return requestClient.post<any>('/purchase-order-detail/getPurchaseOrderDetailAll', data);
}

/**
 * 新增或者更新国外进口商品明细数据
 * 当data中包含id时为更新操作，否则为新增操作
 * @param data 明细数据，包含订单号、序号、商品编码等信息
 * @returns 返回操作结果
 */
export async function addOrEditImportedDetail(data: any) {
  return requestClient.post<any>('/purchase-order/addOrEditImportedDetail', data);
}

/**
 * 删除国外进口商品明细数据
 * 根据ID删除指定的明细记录
 * @param data 包含id的参数对象
 * @returns 返回删除操作的结果
 */
export async function deleteImportedDetail(data: any) {
  return requestClient.post<any>('/purchase-order/deleteImportedDetail', data);
}

/**
 * 下载导入模板
 * @param data 参数
 */
export async function downLoadExcelModel() {
  return requestClient.post<any>('/purchase-order/downLoadExcel');
}

/**
 * 导入模板
 * @param data 参数
 */
export async function importByExcel(data: any) {
  return requestClient.post<any>('/purchase-order/importByExcel', data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 新增导入国外进口的采购的商品或者原材料
 * @param data 明细数据，包含订单号、序号、商品编码等信息
 * @returns 返回操作结果
 */
export async function insertPurchase(data: any) {
  return requestClient.post<any>('/purchase-order/insertPurchase', data);
}

/**
 * 确认采购订单
 * 将订单状态从"新建"更改为"确认"
 * @param data 包含订单ID的参数对象
 * @returns 返回操作结果
 */
export async function confirmPurchaseOrder(data: any) {
  return requestClient.post<any>('/purchase-order/confirmPurchase', data);
}

/**
 * 取消确认采购订单
 * 将订单状态从"确认"更改回"新建"
 * @param data 包含订单ID的参数对象
 * @returns 返回操作结果
 */
export async function cancelConfirmPurchaseOrder(data: any) {
  return requestClient.post<any>('/purchase-order/cancelPurchase', data);
}

