import { requestClient } from '#/api/request';

/**
 * 查询用户
 * @param data 参数
 */
export async function sysUserGet(data: any) {
  return requestClient.post<any>('/user/getUserList', data);
}

/**
 * 添加用户
 * @param data 参数
 */
export async function sysUserSave(data: any) {
  return requestClient.post('/user/insertUser', data);
}
/**
 * 更新账号状态
 * @param data 参数
 */
export async function setAccountStatus(data: any) {
  return requestClient.post('/user/setAccountStatus', data);
}

/**
 * 更新头像
 * @param data 参数
 */
export async function updateAvatar(data: any) {
  return requestClient.post('/user/setUserAvatar', data);
}

/**
 * 修改密码
 * @param data 参数
 */
export async function updatePwd(data: any) {
  return requestClient.post('/user/updateUserPsw', data);
}
