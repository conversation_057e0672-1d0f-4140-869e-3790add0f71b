{"Category": "Main Category", "DeclarationDetail": "Product Library", "Seq": "Sequence No.", "Specification": "Specification & Model", "addDeclaration": "Add new elements for product declaration", "category": "Category", "confirmDelaration": "The row is marked as required, but the specification model is not filled.", "deCode": "Feature coding", "deCodeContetn": "Feature content", "declaration": "Product Declaration Elements", "details": "Declaration Details", "isMust": "Required", "productBrand": "Product Brand", "productCode": "Product Code", "productDesc": "Product Description", "productManagement": "Product Management", "productModel": "Product Model", "productName": "Product Name", "productUnit": "Unit of Measurement", "remark": "Remarks", "subCategory": "Sub Category", "tradingUnit": "Trading Unit", "viewDeclaration": "View the elements of product declaration"}