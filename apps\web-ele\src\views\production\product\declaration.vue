<template>
  <Modal class="w-[70%] h-[60%] min-w-[500px]" :title=title>

    <TableAction :actions="[
      {
        label: $t('basic.add'),
        type: 'primary',
        icon: 'ep:plus',
        //auth: ['product.product.add'],
        onClick: handleAdd.bind(null),
      },
      {
        label: $t('basic.delete'),
        type: 'danger',
        icon: 'ep:delete',
        //auth: ['product.product.add'],
        onClick: handleDel.bind(null),
      },
      {
        label: $t('basic.refresh'),
        type: 'primary',
        icon: 'ep:refresh',
        onClick: getData.bind(null),
      },
      // {
      //   label: $t('basic.reset'),
      //   type: 'primary',
      //   icon: 'ep:refresh',
      //   onClick: handleReset.bind(null),
      // },
    ]"></TableAction>
    <div style="margin-top: 20px; height: 90%;">
      <ClientGridComponent ref="gridRef" :columnDefs="columnDefs" :rowData="rowData" :pageSize="20"
        :defaultColDef="defaultColDef" :rowSelection="rowSelection" />

    </div>


  </Modal>

</template>
<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import ClientGridComponent from '#/components/ag-grid/ClientGridComponent.vue';
import { $t } from '#/locales';
import { TableAction } from '#/components/table-action';
import { getProductDeclaration, addOrEditProductDeclaration } from '#/api/production/productDeclaration';
import { useMessage } from '#/components/elementPlus/useMessage';
const title = ref($t('product.DeclarationDetail'));
const hsCode = ref('');
const productName = ref('');
const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false,
  draggable: true,
  onOpenChange(isOpen) {
    if (isOpen) {
      this.loading = true;
      hsCode.value = modalApi.getData()?.hsCode;
      productName.value = modalApi.getData()?.productName;
      getData();
      this.loading = false;
      title.value = $t('product.DeclarationDetail') + '【' + hsCode.value + '】';
    }
  },
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    useMessage().showMessageBox('confirm', $t('basic.confirmSave'), $t('basic.tips'), 'warning', async (action) => {
      if (action === 'confirm') {
        //结束当前编辑
        gridRef.value?.gridApi.stopEditing();
        // 直接从网格获取所有数据
        const allData: any[] = [];
        gridRef.value?.gridApi.forEachNode((node: any) => {
          allData.push(node.data);
        });


        // ✅ 校验：标记为必填的行，content 不能为空
        const invalidRows = allData.filter(row => row.isMust && !row.content?.trim());

        if (invalidRows.length > 0) {
          useMessage().showMessageBox('alert', ` ${invalidRows.length} ${$t('product.confirmDelaration')}`);
          return;
        }
        // 按照序号排序
        allData.sort((a, b) => a.seq - b.seq);

         // 按declaration分组并组合内容
        const groupedData = allData.reduce((acc, item) => {
          if (!acc[item.declaration]) {
            acc[item.declaration] = [];
          }
          if (item.content) {
            acc[item.declaration].push(item.content);
          }
          return acc;
        }, {});

        // 组合成最终格式：名称：内容1,内容2,内容3
        const formattedData = Object.entries(groupedData)
          .map(([declaration, contents]) => {
            const contentStr = (contents as string[]).join(',');
            return contentStr ? `${declaration}:${contentStr}` : '';
          })
          .filter(str => str) // 移除空字符串
          .join(',');

        // 检查数据是否有变化
        //const hasChanges = JSON.stringify(allData) !== JSON.stringify(rowData.value);

        //if (hasChanges) {
          await addOrEditProductDeclaration({
            hsCode: hsCode.value,
            productName: productName.value,
            items: JSON.stringify(allData),
            speclModel : formattedData
          });
          //useMessage().showMessage('success', $t('production.SaveSuccess'));
        //}

       
        modalApi.sharedData = {
          payload: formattedData
        };
        modalApi.close();
      }
    });
  }
});
const gridRef = ref();

const defaultColDef = {
  //flex: 1,
  editable: true,
};

// Configure row selection
const rowSelection = {
  mode: "multiRow" as "multiRow",
  checkboxes: true,
  headerCheckbox: true,
  copySelectedRows: true,
};
const columnDefs: any[] = [
  {
    headerName: $t('product.Seq'), field: 'seq', maxWidth: 100,
    cellEditorParams: {
      min: 1, // 最小值
      precision: 0 // 整数
    },
    valueParser: (params: any) => {
      // 确保输入的是有效数字
      const newValue = Number(params.newValue);
      return isNaN(newValue) ? params.oldValue : newValue;
    }
  },
  //单独自动调整列
  { headerName: $t('product.declaration'), field: 'declaration', suppressSizeToFit: true, },
  {
    headerName: $t('product.Specification'), field: 'content', width: 135, flex: 1, cellClassRules: {
      'cell-error': (params: any) => {
        return params.data.isMust && !params.value?.trim();
      }
    }
  },
  //增加勾选框列
  {
    headerName: $t('product.isMust'),
    field: 'isMust',
    cellRenderer: 'agCheckboxCellRenderer', // 显示为勾选框
    cellEditor: 'agCheckboxCellEditor',     // 编辑也为勾选框
    width: 100,
    cellStyle: { textAlign: 'center' },
    // 把 null / undefined 当作 false 来显示
    valueGetter: (params: any) => {
      return !!params.data?.isMust;
    },

    // 点勾选后写入 true/false（不是 null）
    valueSetter: (params: any) => {
      const newVal = !!params.newValue;
      if (params.data.isMust !== newVal) {
        params.data.isMust = newVal;
        return true;
      }
      return false;
    }
  },
  { headerName: $t('product.deCode'), field: 'code', suppressSizeToFit: true, },
  { headerName: $t('product.deCodeContetn'), field: 'codeContent', suppressSizeToFit: true, },
];
const rowData = ref<any[]>([])
async function getData() {
  const res = await getProductDeclaration({
    hsCode: hsCode.value
  });
  if (Array.isArray(res)) {
    rowData.value = res;
    //如果没有查到任何数据，则自动添加
    // if (rowData.value.length === 0) {
    //   handleReset();
    // }
  }

}


async function handleAdd() {
  // 获取当前网格中的实际行数
  const currentRowCount = gridRef.value?.gridApi.getDisplayedRowCount() || 0;

  const newRow = {
    seq: currentRowCount + 1,
    declaration: '',
    content: '',
    isMust: false
  };

  // 使用 AG-Grid API 添加新行
  gridRef.value?.gridApi.applyTransaction({
    add: [newRow]
  });

  // 滚动到新添加的行
  if (gridRef.value?.gridApi) {
    setTimeout(() => {
      gridRef.value?.gridApi.ensureIndexVisible(currentRowCount, 'bottom');
    }, 100);
  }
}



async function handleDel() {
  const selectedRows = gridRef.value?.gridApi.getSelectedRows();

  if (!selectedRows || selectedRows.length === 0) {
    useMessage().showMessage('warning', $t('basic.PleaseSelectData'));
    return;
  }

  // 使用 useMessage 进行二次确认
  useMessage().showMessageBox('confirm', $t('basic.confirmDelete'), $t('basic.tips'), 'warning', (action) => {
    if (action === 'confirm') {
      // 使用 ag-grid API 删除选中的行
      gridRef.value?.gridApi.applyTransaction({
        remove: selectedRows
      });
      useMessage().showMessage('success', $t('production.DeleteSuccess'));
    }
  });
};
defineExpose(modalApi);
</script>
<style>
.cell-error {
  border: 1px solid red !important;
  background-color: #fff0f0 !important;
}
</style>
