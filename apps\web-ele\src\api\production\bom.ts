import { requestClient } from '#/api/request';

/**
 * 查询技术单耗成品表数据
 * @param data 参数
 */
export async function getTUCFPList(data: any) {
  return requestClient.post<any>('/bom/getTUCFPList', data);
}

/**
 * 新增技术单耗成品表数据
 * @param data 参数
 */
export async function tucfpAddOrEdit(data: any) {
  return requestClient.post<any>('/bom/tUCFPAddOrEdit', data);
}

/**
 * 查询单挑数据单耗成品表数据
 * @param data 参数
 */
export async function getTucfpInfo(data: any) {
  return requestClient.post<any>('/bom/getTucfpInfo', data);
}

/**
 * 删除单挑数据单耗成品表数据
 * @param data 参数
 */
export async function deleteTucfpInfo(data: any) {
  return requestClient.post<any>('/bom/deleteTucfpInfo', data);
}

/**
 * 查询成品编号对应的料件信息
 * @param data 参数
 */
export async function getTechConsumptionList(data: any) {
  return requestClient.post<any>('/bom/getTechConsumptionList', data);
}

/**
 * 导入原材料代码
 * @param data 参数
 */
export async function importMCode(data: any) {
  return requestClient.post<any>('/bom/importMCode', data);
}


/**
 * 审核
 * @param data 参数
 */
export async function auditBom(data: any) {
  return requestClient.post<any>('/bom/auditBom', data);
}

/**
 * 发布
 * @param data 参数
 */
export async function releaseBom(data: any) {
  return requestClient.post<any>('/bom/releaseBom', data);
}

/**
 * 取消发布
 * @param data 参数
 */
export async function cancelReleaseBom(data: any) {
  return requestClient.post<any>('/bom/cancelReleaseBom', data);
}

/**
 * 删除料件对应料件
 * @param data 参数
 */
export async function deleteBomMCode(data: any) {
  return requestClient.post<any>('/bom/deleteBomMCode', data);
}

/**
 * 下载导入模板
 * @param data 参数
 */
export async function downLoadExcelModel() {
  return requestClient.post<any>('/bom/downLoadExcelModel');
}

/**
 * 导入技术单耗成品表数据
 * @param data 参数
 */
export async function importTechByExcel(data: any) {
  return requestClient.post<any>('/bom/importTechByExcel', data,{
  headers: {
    'Content-Type': 'multipart/form-data'
  }
});
}

/**
 * 保存导入的技术单耗成品表数据
 * @param data 参数
 */
export async function insertBomDataByExcel(data: any) {
  return requestClient.post<any>('/bom/insertBomDataByExcel', data);
}

/**
 * 更新料件对应料件
 * @param data 参数
 */
export async function updateTechConsumption(data: any) {
  return requestClient.post<any>('/bom/updateTechConsumption', data);
}
