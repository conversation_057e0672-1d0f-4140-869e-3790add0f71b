import { requestClient } from '#/api/request';

/**
 * 查询商品编码数据
 * @param data 参数
 */
export async function getProduct(data: any) {
  return requestClient.post<any>('/product/getProduct', data);
}

/**
 * 新增或者更新商品编码数据
 * @param data 参数
 */
export async function addOrEditProduct(data: any) {
  return requestClient.post<any>('/product/addOrEditProduct', data);
}
/**
 * 查询商品编码明细数据
 * @param data 参数
 */
export async function getProductInfo(data: any) {
  return requestClient.post<any>('/product/getProductInfo', data);
}

/**
 * 删除商品编码明细数据
 * @param data 参数
 */
export async function deleteProduct(data: any) {
  return requestClient.post<any>('/product/deleteProduct', data);
}

