import { requestClient } from '#/api/request';

/**
 * 查询组织机构
 * @param data 参数
 */
export async function getOrgList(data: any) {
  return requestClient.post<any>('/organization/getOrgList', data);
}

/**
 * 获取组织机构树结构
 * @param data 参数
 */
export async function getOrgTreeList(data: any) {
  return requestClient.post<any>('/organization/getOrgTreeList', data);
}

/**
 * 新增组织机构
 * @param data 参数
 */
export async function addOrganization(data: any) {
  return requestClient.post<any>('/organization/addOrganization', data);
}

/**
 * 查询组织机构
 * @param data 参数
 */
export async function deleteOrganization(data: any) {
  return requestClient.post<any>('/organization/deleteOrganization', data);
}
