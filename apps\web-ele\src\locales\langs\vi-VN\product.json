{"Category": "<PERSON><PERSON> m<PERSON> ch<PERSON>h", "DeclarationDetail": "<PERSON><PERSON> h<PERSON>ng h<PERSON>a", "Seq": "<PERSON><PERSON> thứ tự", "Specification": "<PERSON>uy cách mẫu mã", "addDeclaration": "Thông số sản phẩm Thông tin sản phẩm <PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON> lo<PERSON>", "confirmDelaration": "$t('basic.confirmSave')", "deCode": "Mã hóa tính năng", "deCodeContetn": "<PERSON><PERSON>i dung đặc sắc", "declaration": "<PERSON><PERSON><PERSON> tố khai báo hàng hóa", "details": "<PERSON> tiết khai báo", "isMust": "<PERSON><PERSON><PERSON> c<PERSON>", "productBrand": "<PERSON><PERSON><PERSON><PERSON><PERSON> hiệu sản phẩm", "productCode": "<PERSON><PERSON> sản phẩm", "productDesc": "<PERSON><PERSON> t<PERSON> sản phẩm", "productManagement": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm", "productModel": "Mẫu sản phẩm", "productName": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "productUnit": "Đơn vị đo lư<PERSON>", "remark": "<PERSON><PERSON><PERSON>", "subCategory": "<PERSON><PERSON><PERSON> phụ thu<PERSON>c", "tradingUnit": "Đơn vị giao dịch", "viewDeclaration": "<PERSON><PERSON> c<PERSON>c yếu tố kê khai hàng hóa"}