import { requestClient } from '#/api/request';

/**
 *  获取文件列表
 * @param data 参数
 */
export async function list(data: any) {
  return requestClient.post<any>('/oss/list', data);
}
/**
 *  上传文件
 * @param data 参数
 */
export async function upload(data: any) {
  return requestClient.post<any>('/oss/upload', data);
}

/**
 *  删除文件
 * @param data 参数
 */
export async function createFolder(data: any) {
  return requestClient.post<any>('/oss/createFolder', data);
}

/**
 *  重命名文件
 * @param data 参数
 */
export async function rename(data: any) {
  return requestClient.post<any>('/oss/rename', data);
}

/**
 *  下载文件
 * @param data 参数
 */
export async function download(data: any) {
  return requestClient.post<any>('/oss/download', data);
}
