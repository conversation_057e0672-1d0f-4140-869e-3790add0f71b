<script lang="ts" setup>
import { ref, h, computed } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import {
  ElMessageBox,
  ElNotification,
  ElButton as Button
} from 'element-plus';
import { $t } from '#/locales';
import { tcmAddOrEdit, getTCMInfo } from '#/api/production/material';
import declaration from '../Bom/declaration.vue';
import selectData from '#/views/common/selectData.vue';
import { useMessage } from '#/components/elementPlus/useMessage';


const isUpdate = ref(false);
const isView = ref(false);

const selectDataRef = ref();
const declarationRef = ref();

const selectConfig = {
  api: '/product/getProduct',
  columns: [
    { headerName: $t('product.productCode'), field: 'hsCode', width: 140 },
    { headerName: $t('product.productName'), field: 'productName', width: 140 },
    { headerName: $t('product.productDesc'), field: 'productDec', width: 140 },
    { headerName: $t('product.productUnit'), field: 'meterUnit', width: 155 },
    { headerName: $t('product.tradingUnit'), field: 'tradUnit', width: 135 },
    { headerName: $t('product.remark'), field: 'Remark', width: 135 },
  ],
  title: $t('basic.pleaseSelect'),
  showSearch: true,
  immediate: false,
  searchPlaceholder: $t('basic.selectInput'),
  multiple: false,
  class: "w-[40%] h-[70%]"
};


// 修改 selectOpen 函数
const selectOpen = async () => {
  try {
    selectDataRef.value.modalApi.open();
    // 监听弹窗关闭事件
    selectDataRef.value.modalApi.onClosed = () => {
      const selectedData = selectDataRef.value.modalApi.sharedData;
      if (selectedData && selectedData.length > 0) {
        const product = selectedData[0];
        // 更新表单数据
        formApi.setValues({
          productCode: product.hsCode,
          productName: product.productName,
          specModel: product.productDec,
          unit: product.meterUnit
        });
      }
    };
  } catch (error) {
    console.error('Error in selectOpen:', error);
    ElNotification({
      type: 'error',
      message: '打开选择窗口失败',
      duration: 2500
    });
  }
};


const [AddForm, formApi] = useVbenForm({
  showDefaultActions: false,
  commonConfig: {

    // 所有表单项
    componentProps: {
      class: 'w-full',
      placeholder: '',
      readonly: isView,
    },
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-12 lg:grid-cols-12',
  layout: 'horizontal',
  collapsed: false,
  schema: [
    {
      fieldName: 'id',
      label: 'ID',
      component: 'Input',
      disabled: true,
      dependencies: {
        triggerFields: ['id'],
        show: false,
      },

    },
    {
      fieldName: 'mCode',
      label: $t('production.RawMaterialCode'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      rules: 'required',
      dependencies: {
        disabled() {
          return isUpdate.value;
        },
        triggerFields: ['isUpdate'] // 添加触发字段
      },
    },
    {
      fieldName: 'mName',
      label: $t('production.RawMaterialName'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
      },
    },
    {
      fieldName: 'intCode',
      label: $t('production.InternalNo'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
      },
    },
    {
      fieldName: 'obsolete',
      label: $t('production.Obsolete'),
      component: "Checkbox",
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
      },
    },
    {
      fieldName: 'originCountry',
      label: $t('production.originCountry'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
      },
    },
    // {
    //   fieldName: 'tradeMode',
    //   label: $t('production.TradeMode'),
    //   component: 'ApiSelect',
    //   // 对应组件的参数
    //   componentProps: {
    //     clearable: true,
    //     api: () => getDropDownList({ codeType: "ImportType" }),
    //     showSearch: true,
    //     filterable:true
    //   },
    //    formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
    // },
    {
      fieldName: 'singleWeight',
      label: $t('production.SingleWeight'),
      component: 'InputNumber',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
        //precision :2,
        style: 'width: 100%'
      },
    },

    {
      label: $t('production.ProdCode'),
      fieldName: "productCode",
      component: "SearchInput",
      formItemClass: 'col-start-1 col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
        searchButtonProps: {
          disabled: isView,
        },
        onSearch: async () => {
          await selectOpen();
        },
      },
    },
    {
      fieldName: 'productName',
      label: $t('production.ProdName'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
      },
    },
    {
      fieldName: 'unit',
      label: $t('production.Unit'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
      },
    },
     {
      fieldName: 'btn',
      label: '',
      component: () => {
        return h(
          'div',
          {},
          h(
            Button,
            {
              type: 'primary',
              onClick: openDelation.bind(null),
            },
            {
              default() {
                return $t('production.selectDec');
              },
            },
          ),
        );
      },
      dependencies: {
        triggerFields: ['isView'],
        if: () => !isView.value, 
      },
      formItemClass: 'col-start-1 col-span-12',
    },
    {
      fieldName: 'specModel',
      label: $t('production.SpecModel'),
      component: 'Input',
      formItemClass: 'col-span-12',
      componentProps: {
        type: 'textarea',
        readonly: true
      },

    },
   

    {
      fieldName: 'width',
      label: $t('production.Width'),
      component: 'Input',
      formItemClass: 'col-start-1 col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
        type: 'number',
      },
    },
    {
      fieldName: 'gsm',
      label: $t('production.GSM'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
        type: 'number',
      },
    },
    {
      fieldName: 'matItemNo',
      label: $t('production.MatItemNo'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      componentProps: {
      },
    },
    {
      fieldName: 'createUserName',
      label: $t('basic.createUserName'),
      component: 'Input',
      formItemClass: 'col-start-1 col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      disabled: true,
      componentProps: {
        placeholder: null,
      },
    },
    {
      fieldName: 'createTime',
      label: $t('basic.createTime'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      disabled: true,
      componentProps: {
        placeholder: null,
      },
    },
    {
      fieldName: 'updateUserName',
      label: $t('production.HSUpdater'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      disabled: true,
      componentProps: {
        placeholder: null,
      },
    },
    {
      fieldName: 'updateTime',
      label: $t('production.HSTimeUpdated'),
      component: 'Input',
      formItemClass: 'col-span-12 md:col-span-6 lg:col-span-4 2xl:col-span-3',
      disabled: true,
      componentProps: {
        placeholder: null,
      },
    },


  ]
})
const record = ref();


const openDelation = async () => {
  //打开申报要素的界面
  //检查是否输入有规格型号值
  //复制一个新的页面，只需要保留选择和确定返回的值
  //console.log(formApi.getValues);
  const values = await formApi.getValues();
  console.log(values);
  if (!values.mCode) {
    useMessage().showMessage("warning", $t('production.pleaseSeMcode'));
    return;
  }
  if (!values.productCode) {
    useMessage().showMessage("warning",$t('production.pleaseSelectProductCode'));
    return;
  }
  declarationRef.value.setData({
    hsCode: values.productCode,
    mCode:values.mCode,
    productName: values.productName
  });

  declarationRef.value.open();
  declarationRef.value.onClosed = () => {
    const selectedData = declarationRef.value.sharedData;
    if (selectedData.payload && selectedData.payload.length > 0) {
      // 更新表单数据
      formApi.setValues({
        specModel: selectedData.payload,
      });

    }


  };
};


const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false,
  draggable: true,
  onOpenChange(isOpen) {
    if (isOpen) {
      this.loading = true;
      record.value = modalApi.getData()?.record || {};
      isUpdate.value = modalApi.getData()?.pageType === 'edit';
      isView.value = modalApi.getData()?.pageType === 'view';
      modalApi.setState({ showConfirmButton: !isView.value });
      if (isUpdate.value || isView.value) {
        getTCMInfo({ id: record.value.id }).then((res: any) => {
          formApi.setValues(res);
        });
      }
      this.loading = false;
    }
  },
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    ElMessageBox.confirm(
      '确定要保存当前输入数据吗?',
      'Warning',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(() => {
        formApi.validate().then(async (e: any) => {
          if (e.valid) {
            const values = await formApi.getValues();
            modalApi.setState({ confirmLoading: true });
            await tcmAddOrEdit(values);
            ElNotification({ duration: 2500, message: $t('production.SaveSuccess'), type: 'success' });
            //modalApi.setState({confirmLoading: false });
            modalApi.close();
          }
        }).finally(() => {
          modalApi.setState({ confirmLoading: false });;

        })
      })
      .catch(() => {
        ElNotification({ duration: 2500, message: $t('production.CancelSuccess'), type: 'info' });
      });
  }
});
const title = $t('production.Material');
defineExpose(modalApi);
</script>
<template>
  <Modal class="w-[70%]" :title=title>

    <AddForm />

    <selectData ref="selectDataRef" v-bind="selectConfig" />
    <declaration ref="declarationRef" />
  </Modal>
</template>
