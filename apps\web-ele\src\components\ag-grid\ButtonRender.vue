<template>
  <div class="button-render-container">
    <!-- 下拉菜单模式 -->
    <template v-if="dropdownConfig">
      <!-- 主要操作按钮（支持多个） -->
      <template v-for="(action, index) in primaryActions" :key="`primary-${index}`">
        <ElPopconfirm
          v-if="action.popConfirm"
          v-bind="getPopConfirmProps(action.popConfirm)"
        >
          <template #reference>
            <ElButton
              :type="getString(action.type ?? 'default') as any"
              :size="action.size || 'small'"
              :class="getString(action.class ?? '')"
              :disabled="getDisabled(action.disabled)"
            >
              {{ getLabel(action) }}
            </ElButton>
          </template>
        </ElPopconfirm>
        <ElButton
          v-else
          :type="getString(action.type ?? 'default') as any"
          :size="action.size || 'small'"
          @click="handleAction(action.callback, action.eventName)"
          :class="getString(action.class ?? '')"
          :disabled="getDisabled(action.disabled)"
        >
          {{ getLabel(action) }}
        </ElButton>
      </template>

      <!-- 下拉菜单 -->
      <ElDropdown
        v-if="visibleDropdownItems.length > 0"
        @command="handleDropdownCommand"
        trigger="click"
      >
        <ElButton
          :size="dropdownConfig.size || 'small'"
          type="default"
        >
          {{ dropdownConfig.label || '...' }}
        </ElButton>

        <template #dropdown>
          <ElDropdownMenu>
            <ElDropdownItem
              v-for="(item, index) in visibleDropdownItems"
              :key="index"
              :command="index"
              :divided="item.divided"
              :disabled="getDisabled(item.disabled)"
            >
              {{ getLabel(item) }}
            </ElDropdownItem>
          </ElDropdownMenu>
        </template>
      </ElDropdown>
    </template>

    <!-- 原有的按钮模式 -->
    <template v-else>
      <template v-for="(action, index) in visibleActions" :key="index">
        <ElPopconfirm
          v-if="action.popConfirm"
          v-bind="getPopConfirmProps(action.popConfirm)"
        >
          <template #reference>
            <ElButton
              :type="getString(action.type ?? 'default') as any"
              :size="action.size || 'small'"
              :class="getString(action.class ?? '')"
              :disabled="getDisabled(action.disabled)"
            >
              {{ getLabel(action) }}
            </ElButton>
          </template>
        </ElPopconfirm>
        <ElButton
          v-else
          :type="getString(action.type ?? 'default') as any"
          :size="action.size || 'small'"
          @click="handleAction(action.callback, action.eventName)"
          :class="getString(action.class ?? '')"
          :disabled="getDisabled(action.disabled)"
        >
          {{ getLabel(action) }}
        </ElButton>
      </template>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ElButton, ElPopconfirm, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus';
import { defineProps, defineEmits, computed } from 'vue';
import { type ICellRendererParams } from 'ag-grid-community';
import { useAccess } from '@vben/access';
import { isFunction } from '@vben/utils';

const { hasAccessByCodes } = useAccess();

interface PopConfirm {
  title?: string;
  description?: string;
  confirm?: Function;
  cancel?: Function;
  confirmButtonText?: string;
  cancelButtonText?: string;
  icon?: string;
  [key: string]: any;
}

interface Action {
  label: string | ((params: ICellRendererParams) => string);
  callback: Function;
  auth?: string[];
  type?: string | ((params: ICellRendererParams) => string);
  size?: 'small' | 'default' | 'large';
  class?: string | ((params: ICellRendererParams) => string);
  eventName?: string;
  popConfirm?: PopConfirm;
  disabled?: boolean | ((params: ICellRendererParams) => boolean);
  show?: boolean | ((data: any) => boolean);
  divided?: boolean; // 下拉菜单项分割线
}

// 下拉菜单配置接口
interface DropdownConfig {
  label?: string; // 下拉按钮文本，默认"更多"
  size?: 'small' | 'default' | 'large';
  primaryActions?: Action[]; // 主要操作按钮（支持多个）
  menuItems: Action[]; // 下拉菜单项
}

interface ActionCellProps {
  params: ICellRendererParams & {
    actions?: Action[] | ((params: ICellRendererParams) => Action[]) | DropdownConfig; // 支持传统数组、函数或下拉配置
    dropdown?: DropdownConfig; // 下拉菜单配置（向后兼容）
  };
}

const props = defineProps<{
  params: ActionCellProps['params'];
}>();

// 定义 emit 事件
const emit = defineEmits<{
  (event: 'actionTriggered', eventName: string, data: any): void;
}>();

// 检测是否为下拉配置
function isDropdownConfig(config: any): config is DropdownConfig {
  return config && (config.primaryActions || config.menuItems || config.label !== undefined);
}

// 智能解析配置
const parsedConfig = computed(() => {
  const actionsConfig = typeof props.params?.actions === 'function'
    ? props.params.actions(props.params)
    : props.params?.actions;

  // 优先使用 dropdown 配置（向后兼容）
  if (props.params?.dropdown) {
    return {
      isDropdown: true,
      config: props.params.dropdown
    };
  }

  // 检测 actions 是否为下拉配置
  if (isDropdownConfig(actionsConfig)) {
    return {
      isDropdown: true,
      config: actionsConfig as DropdownConfig
    };
  }

  // 传统按钮模式
  return {
    isDropdown: false,
    config: (actionsConfig as Action[]) || []
  };
});

// 下拉菜单配置
const dropdownConfig = computed(() =>
  parsedConfig.value.isDropdown ? parsedConfig.value.config as DropdownConfig : null
);

// 传统按钮列表
const allActions = computed(() =>
  !parsedConfig.value.isDropdown ? parsedConfig.value.config as Action[] : []
);

// 过滤可见的动作
const visibleActions = computed(() => {
  return allActions.value.filter(action =>
    hasAccessByCodes(action.auth ?? []) && getShow(action.show)
  );
});

// 主要操作按钮（下拉模式）
const primaryActions = computed(() => {
  if (!dropdownConfig.value?.primaryActions) return [];
  return dropdownConfig.value.primaryActions.filter(action =>
    hasAccessByCodes(action.auth ?? []) && getShow(action.show)
  );
});

// 可见的下拉菜单项
const visibleDropdownItems = computed(() => {
  if (!dropdownConfig.value?.menuItems) return [];
  return dropdownConfig.value.menuItems.filter(action =>
    hasAccessByCodes(action.auth ?? []) && getShow(action.show)
  );
});

// 处理按钮点击
function handleAction(callback: Function, eventName?: string) {
  if (callback) {
    callback(props.params.data);
  }
  if (eventName) {
    emit('actionTriggered', eventName, props.params.data);
  }
}

// 处理下拉菜单命令
function handleDropdownCommand(index: number) {
  const menuItem = visibleDropdownItems.value[index];
  if (menuItem) {
    handleAction(menuItem.callback, menuItem.eventName);
  }
}

// 工具函数
function getLabel(action: Action) {
  if (typeof action.label === 'function') {
    return action.label(props.params);
  }
  return action.label;
}

function getString(content: string | ((params: ICellRendererParams) => string)) {
  return typeof content === 'function' ? content(props.params) : content;
}

function getPopConfirmProps(attrs: PopConfirm) {
  const originAttrs: any = { ...attrs };
  if (attrs.confirm && isFunction(attrs.confirm)) {
    originAttrs.onConfirm = () => attrs.confirm?.(props.params.data);
    delete originAttrs.confirm;
  }
  if (attrs.cancel && isFunction(attrs.cancel)) {
    originAttrs.onCancel = () => attrs.cancel?.(props.params.data);
    delete originAttrs.cancel;
  }
  return originAttrs;
}

function getDisabled(disabled?: boolean | ((params: ICellRendererParams) => boolean)) {
  if (typeof disabled === 'function') {
    return disabled(props.params);
  }
  return disabled ?? false;
}

function getShow(show?: boolean | ((data: any) => boolean)) {
  if (typeof show === 'function') {
    return show(props.params.data);
  }
  return show ?? true;
}

</script>

<style scoped>
::v-global(.el-dropdown) {
  vertical-align: unset !important;
  padding: 5px 11px;
}
</style>
