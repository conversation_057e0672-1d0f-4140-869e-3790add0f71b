import { requestClient } from '#/api/request';

/**
 * 查询角色
 * @param data 参数
 */
export async function sysRoleGet(data: any) {
  return requestClient.post<any>('/role/getRoleList', data);
}

/**
 * 添加角色
 * @param data 参数
 */
export async function sysRoleSave(data: any) {
  return requestClient.post('/role/insertRole', data);
}

/**
 * 获取角色的授权码
 * @param data 参数
 */
export async function sysRolePerm(data: any) {
  return requestClient.post('/role/getPermissions', data);
}

/**
 * 对角色进行授权
 * @param data 参数
 */
export async function sysRolePermEdit(data: any) {
  return requestClient.post('/role/grantPermissions', data);
}

/**
 * 根据角色获取用户信息
 * @param data 参数
 */
export async function sysRoleUser(data: any) {
  return requestClient.post('/role/getRoleUser', data);
}

/**
 * 获取当前用户角色关系ID进行删除
 * @param data 参数
 */
export async function deleteUserRole(data: any) {
  return requestClient.post('/role/deleteUserRole', data);
}

/**
 * 获取当前用户角色关系ID进行删除
 * @param data 参数
 */
export async function deleteRole(data: any) {
  return requestClient.post('/role/deleteRole', data);
}

/**
 * 为角色添加用户
 * @param data 参数
 */
export async function insertRoleUser(data: any) {
  return requestClient.post('/role/insertRoleUser', data);
}
/**
 * 查询未拥有角色的用户
 * @param data 参数
 */
export async function getNotRoleUser(data: any) {
  return requestClient.post('/role/getNotRoleUser', data);
}


/**
 * 获取用户的所有角色
 * @param data 参数
 */
export async function getRoleByUser(data: any) {
  return requestClient.post('/role/getRoleByUser', data);
}

/**
 * 为用户进行角色授权
 * @param data 参数
 */
export async function insertRoleByUser(data: any) {
  return requestClient.post('/role/insertRoleByUser', data);
}

