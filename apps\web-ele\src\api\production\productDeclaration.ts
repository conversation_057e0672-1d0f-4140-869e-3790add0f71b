import { requestClient } from '#/api/request';

/**
 * 查询商品编码的商品申报要素信息
 * @param data 参数
 */
export async function getProductDeclaration(data: any) {
  return requestClient.post<any>('/product-declaration/getProductDeclaration', data);
}

/**
 * 新增或者修改当前商品申报要素信息
 * @param data 参数
 */
export async function addOrEditProductDeclaration(data: any) {
  return requestClient.post<any>('/product-declaration/addOrEditProductDeclaration', data);
}

/**
 * 查询料号级对应的商品要素信息
 * @param data 参数
 */
export async function getProductDecMataial(data: any) {
  return requestClient.post<any>('/product-declaration/getProductDecMataial', data);
}

/**
 * 新增或者修改当前商品申报要素信息
 * @param data 参数
 */
export async function addOrEditMaterialDeclaration(data: any) {
  return requestClient.post<any>('/product-declaration/addOrEditMaterialDeclaration', data);
}

