import { ElMessage, ElNotification, ElMessageBox } from 'element-plus';

type MessageType = 'success' | 'warning' | 'info' | 'error';
type MessageBoxType = 'confirm' | 'alert' | 'prompt';

export function useMessage() {
  // ElMessage 统一调用方法
  function showMessage(type: MessageType, message: string, duration = 2500) {
    ElMessage({
      type,
      message,
      duration,
    });
  }

  // ElNotification 统一调用方法
  function showNotification(type: MessageType, message: string, duration = 2500) {
    ElNotification({
      type,
      message,
      duration,
    });
  }

  // ElMessageBox 统一调用方法
  function showMessageBox(
    type: MessageBoxType,
    message: string,
    title: string = 'Tips',
    boxType: 'success' | 'warning' | 'info' | 'error' = 'warning', // ElMessageBox.alert的类型
    callback?: (action: 'confirm' | 'cancel' | 'input' | null, value?: string) => void
  ) {
    const options: any = {
      title,
      message,
      type: 'warning', // 默认警告类型
    };

    // 根据不同的 type 显示对话框
    if (type === 'confirm') {
      ElMessageBox.confirm(message, title, options)
        .then(() => {
          if (callback) callback('confirm');
        })
        .catch(() => {
          if (callback) callback('cancel');
        });
    } else if (type === 'alert') {
      ElMessageBox.alert(message, title, { ...options, type: boxType })  // 使用传入的boxType
        .then(() => {
          if (callback) callback('confirm');
        });
    } else if (type === 'prompt') {
      ElMessageBox.prompt(message, title, { ...options, type: boxType })
        .then(({ value }) => {
          if (callback) callback('confirm', value);
        })
        .catch(() => {
          if (callback) callback('cancel');
        });
    }
  }

  return {
    showMessage,
    showNotification,
    showMessageBox, // 返回新的方法
  };
}
