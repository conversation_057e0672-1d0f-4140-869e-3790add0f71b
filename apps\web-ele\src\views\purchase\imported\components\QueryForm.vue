<template>
  <div class="mb-4">
    <Form />
  </div>
</template>

<script lang="ts" setup>

import { useVbenForm } from '#/adapter/form';
import { $t } from '#/locales';

/**
 * 定义组件事件
 * search: 搜索事件，传递搜索参数
 * reset: 重置事件，清空搜索参数
 */
const emit = defineEmits(['search', 'reset']);

/**
 * 创建表单组件
 * Form: 表单组件
 */
const [Form] = useVbenForm({
  // 显示默认的提交和重置按钮
  showDefaultActions: true,
  // 启用折叠功能
  showCollapseButton: true,
  // 表单布局
  wrapperClass: 'grid-cols-12 md:grid-cols-1 lg:grid-cols-4',
  // 折叠时显示的行数
  collapsedRows: 1,
  // 所有表单项的通用配置
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
   layout: 'horizontal',
  // 表单字段定义
  schema: [
    {
      fieldName: 'orderNo', // 进口订单号
      component: 'Input',
      label: $t('purchase.importOrderNo'),
    },
    {
      fieldName: 'status', // 状态
      component: 'Select',
      label: $t('purchase.status'),
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('purchase.new'), value: 'new' }, // 新建
          { label: $t('purchase.confirm'), value: 'confirm' }, // 确认
          { label: $t('purchase.store'), value: 'store' }, // 入库
        ],
      },
    },
      {
      fieldName: 'poNo', 
      component: 'Input',
      label: $t('purchase.poNo'),
    },
     {
      fieldName: 'importDateRange', // 订单时间范围
      component: 'DatePicker',
      label: $t('purchase.orderDate'),
      componentProps: {
        type: 'daterange',
        valueFormat: 'YYYY-MM-DD',
        rangeSeparator: '-',
        startPlaceholder: $t('basic.startDate'),
        endPlaceholder: $t('basic.endDate'),
      },
    },
  ],
  // 按钮文本
  submitButtonOptions: {
    content: $t('basic.search')
  },
  resetButtonOptions: {
    content: $t('basic.reset')
  },
  // 提交处理函数
  handleSubmit: (values) => {
    // 处理日期范围，将日期范围转换为开始日期和结束日期
    if (values.importDateRange && values.importDateRange.length === 2) {
      values.startDate = values.importDateRange[0];
      values.endDate = values.importDateRange[1];
    }
    delete values.importDateRange;

    // 触发搜索事件，传递处理后的表单值
    emit('search', values);
  },
  // 重置处理函数
  handleReset: () => {
    // 触发重置事件
    emit('reset');
  },
});
</script>
