<template>
  <el-input
    v-model="value"
    :disabled="!isEditing"
    :placeholder="''"
    @input="onInput"
    style="width: 100%; height: 100%;"
  />
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import type { ICellRendererParams } from 'ag-grid-community';

declare global {
  interface Window {
    editingRowId: string | null;
  }
}

const props = defineProps<{
  params: ICellRendererParams;
}>();

const value = ref(props.params.value);
const isEditing = ref(props.params.data.id === window.editingRowId);

const onInput = (e: string) => {
  value.value = e;
  props.params.setValue?.(e);
};

watch(() => props.params.value, (newValue) => {
  value.value = newValue;
});

watch(() => props.params.data.id, () => {
  isEditing.value = props.params.data.id === window.editingRowId;
});
</script> 
