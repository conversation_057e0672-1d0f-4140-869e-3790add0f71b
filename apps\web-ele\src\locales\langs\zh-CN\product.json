{"Category": "所属大类", "DeclarationDetail": "商品库", "Seq": "序号", "Specification": "规格型号", "addDeclaration": "新增商品申报要素", "category": "分类", "confirmDelaration": " 行标记为必填，但未填写规格型号", "deCode": "要素编码", "deCodeContetn": "要素内容", "declaration": "商品申报要素", "details": "申报要素", "isMust": "必填", "productBrand": "商品品牌", "productCode": "商品编码", "productDesc": "商品描述", "productManagement": "商品管理", "productModel": "商品型号", "productName": "商品名称", "productUnit": "计量单位", "remark": "备注", "subCategory": "所属类型", "tradingUnit": "交易单位", "viewDeclaration": "查看商品申报要素"}